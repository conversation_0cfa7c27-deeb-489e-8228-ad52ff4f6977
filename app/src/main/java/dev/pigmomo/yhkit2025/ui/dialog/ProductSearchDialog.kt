package dev.pigmomo.yhkit2025.ui.dialog

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import dev.pigmomo.yhkit2025.api.model.search.SearchResult
import dev.pigmomo.yhkit2025.api.model.search.SearchResponse
import dev.pigmomo.yhkit2025.api.model.cart.CartItem
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.model.cart.CartModelTypes
import dev.pigmomo.yhkit2025.api.model.cart.CartModelWrapper
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor

/**
 * 商品搜索对话框组件
 *
 * @param cartItems 购物车数据列表
 * @param onDismiss 对话框关闭回调
 * @param onProductClick 商品点击回调
 * @param onAddToCart 添加到购物车回调，参数为商品配置字符串
 * @param onReduceFromCart 从购物车减少商品回调，参数为Product对象
 * @param onSearch 搜索回调，参数为 (keyword: String, page: Int, onResult: (SearchResponse?, String?) -> Unit)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductSearchDialog(
    cartItems: List<CartItem> = emptyList(),
    onDismiss: () -> Unit = {},
    onProductClick: (SearchResult) -> Unit = {},
    onAddToCart: (String) -> Unit = {},
    onReduceFromCart: (Product) -> Unit = {},
    onSearch: (String, Int, (SearchResponse?, String?) -> Unit) -> Unit = { _, _, _ -> }
) {
    // 内部状态管理
    var searchKeyword by remember { mutableStateOf("") }
    var searchResults by remember { mutableStateOf<List<SearchResult>>(emptyList()) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var isLoadingMore by remember { mutableStateOf(false) }
    var hasSearched by remember { mutableStateOf(false) } // 跟踪是否已执行过搜索

    // 分页信息
    var currentPage by remember { mutableIntStateOf(0) }
    var totalPage by remember { mutableIntStateOf(0) }
    var hasNext by remember { mutableIntStateOf(0) }
    var nextPage by remember { mutableIntStateOf(1) }

    // 搜索函数
    val performSearch = { keyword: String, page: Int, isLoadMore: Boolean ->
        if (isLoadMore) {
            isLoadingMore = true
        } else {
            isLoading = true
            errorMessage = null
            hasSearched = true // 标记已执行搜索
            // 重置分页信息
            currentPage = 0
            totalPage = 0
            nextPage = 1
        }

        onSearch(keyword, page) { response, error ->
            if (error != null) {
                errorMessage = error
            } else if (response != null) {
                val newResults = response.data?.results ?: emptyList()

                // 更新分页信息
                response.data?.let { data ->
                    data.pageBase?.let { pageBase ->
                        nextPage = pageBase.nextPage ?: 1
                        hasNext = pageBase.hasNext ?: 0
                    }
                    totalPage = data.totalpage ?: 0
                    currentPage = data.page ?: 0
                }

                // 更新搜索结果
                if (isLoadMore) {
                    searchResults = searchResults + newResults
                } else {
                    searchResults = newResults
                }
            }

            if (isLoadMore) {
                isLoadingMore = false
            } else {
                isLoading = false
            }
        }
    }

    // 加载更多函数
    val loadMore = {
        if (searchKeyword.isNotBlank() && hasNext == 1 && !isLoadingMore) {
            performSearch(searchKeyword, nextPage, true)
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("商品搜索") },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 500.dp)
            ) {
                // 搜索输入框
                OutlinedTextField(
                    value = searchKeyword,
                    onValueChange = { searchKeyword = it },
                    label = { Text("商品关键词") },
                    trailingIcon = {
                        if (isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            IconButton(
                                onClick = {
                                    performSearch(searchKeyword, 0, false)
                                },
                                enabled = searchKeyword.isNotBlank()
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Search,
                                    contentDescription = "搜索",
                                    tint = if (searchKeyword.isNotBlank()) {
                                        MaterialTheme.colorScheme.primary
                                    } else {
                                        MaterialTheme.colorScheme.onSurfaceVariant
                                    }
                                )
                            }
                        }
                    },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Search
                    ),
                    keyboardActions = KeyboardActions(
                        onSearch = {
                            performSearch(searchKeyword, 0, false)
                        }
                    ),
                    modifier = Modifier
                        .fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                )

                // 错误信息显示
                errorMessage?.let { error ->
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp)
                    ) {
                        Text(
                            text = error,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }

                // 搜索结果列表
                if (searchResults.isNotEmpty()) {
                    // 显示搜索结果统计和分页信息
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "搜索结果 (${searchResults.size})",
                            style = MaterialTheme.typography.titleSmall
                        )

                        if (totalPage > 0) {
                            Text(
                                text = "第${currentPage + 1}页/共${totalPage}页",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }

                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(searchResults) { result ->
                            ProductSearchItem(
                                searchResult = result,
                                cartItems = cartItems,
                                onClick = { onProductClick(result) },
                                onAddToCart = { productConfig ->
                                    onAddToCart(productConfig)
                                },
                                onReduceFromCart = { product ->
                                    onReduceFromCart(product)
                                }
                            )
                        }

                        // 加载更多项
                        if (hasNext == 1) {
                            item {
                                LoadMoreItem(
                                    isLoading = isLoadingMore,
                                    onLoadMore = loadMore
                                )
                            }
                        } else if (searchResults.isNotEmpty() && totalPage > 0) {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "已加载全部结果",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }
                } else if (!isLoading && hasSearched && searchKeyword.isNotBlank() && errorMessage == null) {
                    // 空状态显示 - 只有在已执行搜索且结果为空时才显示
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "搜索结果为空",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = { },
        dismissButton = null
    )
}

/**
 * 商品搜索结果项组件
 */
@SuppressLint("DefaultLocale")
@Composable
private fun ProductSearchItem(
    searchResult: SearchResult,
    cartItems: List<CartItem> = emptyList(),
    onClick: () -> Unit,
    onAddToCart: (String) -> Unit = {},
    onReduceFromCart: (Product) -> Unit = {}
) {
    val skuBlock = searchResult.skuBlock
    if (skuBlock == null) return

    val ribbonTags = skuBlock.tag?.ribbonTags

    // 查找商品在购物车中的数量和Product对象
    val cartProductInfo = findProductInCart(skuBlock.skuCode ?: "", cartItems)
    val cartQuantity = cartProductInfo?.first ?: 0
    val cartProduct = cartProductInfo?.second

    Card(
        modifier = Modifier
            .fillMaxWidth(),
        colors = if (searchResult.skuBlock.inStock == 1) cardThemeOverlay() else CardDefaults.cardColors(
            containerColor = Color.Gray.copy(0.1f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {

            Box(
                modifier = Modifier.size(60.dp)
            ) {
                // 商品图片
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(skuBlock.cover?.imageUrl ?: skuBlock.cover?.url)
                        .crossfade(true)
                        .build(),
                    contentDescription = skuBlock.title,
                    modifier = Modifier
                        .size(60.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(Color.White),
                    contentScale = ContentScale.Crop
                )

                // 标签
                ribbonTags?.let { tags ->
                    tags.forEach { tag ->
                        Text(
                            text = tag.text ?: "",
                            fontSize = 8.sp,
                            lineHeight = 8.sp,
                            color = Color.White,
                            modifier = Modifier
                                .clip(RoundedCornerShape(4.dp))
                                .background(Color.Red)
                                .padding(2.dp)
                                .align(Alignment.BottomEnd)
                        )
                    }
                }

            }

            Spacer(modifier = Modifier.width(12.dp))

            // 商品信息
            Column(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onClick() }
            ) {
                // 商品标题
                Text(
                    text = skuBlock.title ?: "未知商品",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    modifier = Modifier.horizontalScroll(rememberScrollState())
                )

                // 商品副标题
                skuBlock.recAttribute?.let { recAttribute ->
                    Text(
                        text = recAttribute.joinToString("|"),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier
                            .padding(top = 2.dp)
                            .horizontalScroll(rememberScrollState())
                    )
                }

                // 价格信息和按钮在同一行
                skuBlock.price?.let { price ->
                    val priceText = when {
                        price.price != null -> "¥${price.price}"
                        price.value != null -> "¥${String.format("%.2f", price.value / 100.0)}"
                        else -> "价格未知"
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 4.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = priceText,
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )

                        // 只有有库存的商品才显示按钮
                        if (skuBlock.inStock == 1) {
                            // 根据购物车中的数量显示不同的UI
                            if (cartQuantity > 0) {
                                // 显示数量控制器（与购物车样式保持一致）
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    // 减少按钮
                                    Box(
                                        modifier = Modifier
                                            .size(16.dp)
                                            .clip(RoundedCornerShape(2.dp))
                                            .background(Color(0xFFF5F5F5))
                                            .clickable {
                                                cartProduct?.let { product ->
                                                    onReduceFromCart(product)
                                                }
                                            },
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = "-",
                                            fontSize = 12.sp,
                                            lineHeight = 12.sp,
                                            color = Color.Black
                                        )
                                    }

                                    // 数量显示
                                    Box(
                                        modifier = Modifier
                                            .width(24.dp)
                                            .height(16.dp),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = "${cartQuantity / 100}",
                                            lineHeight = 12.sp,
                                            fontSize = 12.sp
                                        )
                                    }

                                    // 增加按钮
                                    Box(
                                        modifier = Modifier
                                            .size(16.dp)
                                            .clip(RoundedCornerShape(2.dp))
                                            .background(Color(0xFFF5F5F5))
                                            .clickable {
                                                val productId = skuBlock.skuCode ?: ""
                                                if (productId.isNotEmpty()) {
                                                    val productConfig = "$productId,1,0,"
                                                    onAddToCart(productConfig)
                                                }
                                            },
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = "+",
                                            fontSize = 12.sp,
                                            lineHeight = 12.sp,
                                            color = Color.Black
                                        )
                                    }
                                }
                            } else {
                                // 显示加购按钮（与购物车样式保持一致）
                                Box(
                                    modifier = Modifier
                                        .height(16.dp)
                                        .clip(RoundedCornerShape(2.dp))
                                        .background(Color(0xFFF5F5F5))
                                        .clickable {
                                            val productId = skuBlock.skuCode ?: ""
                                            if (productId.isNotEmpty()) {
                                                val productConfig = "$productId,1,0,"
                                                onAddToCart(productConfig)
                                            }
                                        }
                                        .padding(horizontal = 8.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "加购",
                                        fontSize = 12.sp,
                                        lineHeight = 12.sp,
                                        color = Color.Black
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 加载更多组件
 */
@Composable
private fun LoadMoreItem(
    isLoading: Boolean,
    onLoadMore: () -> Unit
) {
    LaunchedEffect(Unit) {
        if (!isLoading) {
            onLoadMore()
        }
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        if (isLoading) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
                Text(
                    text = "加载中...",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else {
            Text(
                text = "正在加载更多...",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 查找商品在购物车中的数量和Product对象
 * @param skuCode 商品SKU代码
 * @param cartItems 购物车数据列表
 * @return Pair<数量, Product对象> 如果找到，否则返回null
 */
private fun findProductInCart(skuCode: String, cartItems: List<CartItem>): Pair<Int, Product>? {
    if (skuCode.isEmpty()) return null

    cartItems.forEach { cartItem ->
        cartItem.cartModels.forEach { cartModel ->
            if (cartModel.modelType == CartModelTypes.PRODUCT_ITEM) {
                val cartModelWrapper = CartModelWrapper(
                    data = cartModel.data,
                    modelType = cartModel.modelType
                )
                val product = cartModelWrapper.getProduct()
                if (product != null) {
                    // 匹配商品ID或原始SKU代码
                    if (product.id == skuCode || product.originalskucode == skuCode) {
                        return Pair(product.num, product)
                    }
                }
            }
        }
    }
    return null
}
